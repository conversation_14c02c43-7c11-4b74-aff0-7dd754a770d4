{"version": "0.2.0", "configurations": [{"name": "Launch user-service", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/cmd/user-service/main.go", "envFile": "${workspaceFolder}/dev.env", "env": {"APP_PORT": "8080"}, "preLaunchTask": "daprd", "postDebugTask": "daprd-stop", "daprd": {"appId": "user-service", "appPort": 8080, "daprPort": 3500, "resourcesPath": "${workspaceFolder}/resources"}, "cwd": "${workspaceFolder}"}]}