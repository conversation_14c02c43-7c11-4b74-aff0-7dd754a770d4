{"version": "2.0.0", "tasks": [{"label": "daprd", "type": "shell", "command": "dapr", "args": ["run", "--app-id", "user-service", "--app-port", "8080", "--resources-path", "${workspaceFolder}/resources", "--dapr-http-port", "3500", "--dapr-grpc-port", "50001"], "isBackground": true, "problemMatcher": {"pattern": [{"regexp": ".", "file": 1, "location": 2, "message": 3}], "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "."}}}, {"label": "daprd-stop", "type": "shell", "command": "pkill", "args": ["-f", "dapr"], "problemMatcher": [], "presentation": {"reveal": "never", "close": true}}]}