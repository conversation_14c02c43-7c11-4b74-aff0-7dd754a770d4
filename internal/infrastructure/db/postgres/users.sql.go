// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: users.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createUser = `-- name: CreateUser :one
INSERT INTO
    users (
        identifier,
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        created_at,
        updated_at
    )
VALUES
    (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
RETURNING
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
`

type CreateUserParams struct {
	Identifier string             `json:"identifier"`
	Approved   bool               `json:"approved"`
	Banned     bool               `json:"banned"`
	MetaData   []byte             `json:"meta_data"`
	Roles      []string           `json:"roles"`
	ExpiresAt  pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.Identifier,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.Roles,
		arg.ExpiresAt,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getActiveUserById = `-- name: GetActiveUserById :one
SELECT
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM
    users
WHERE
    id = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT
    1
`

func (q *Queries) GetActiveUserById(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getActiveUserById, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getActiveUserByIdentifier = `-- name: GetActiveUserByIdentifier :one
SELECT
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM
    users
WHERE
    identifier = $1
    AND banned = false
    AND expires_at > CURRENT_TIMESTAMP
    AND deleted_at IS NULL
LIMIT
    1
`

func (q *Queries) GetActiveUserByIdentifier(ctx context.Context, identifier string) (User, error) {
	row := q.db.QueryRow(ctx, getActiveUserByIdentifier, identifier)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getSafeUserById = `-- name: GetSafeUserById :one
SELECT
    id,
    identifier,
    approved,
    banned,
    roles
FROM
    users
WHERE
    id = $1
    AND deleted_at IS NULL
LIMIT
    1
`

type GetSafeUserByIdRow struct {
	ID         int64    `json:"id"`
	Identifier string   `json:"identifier"`
	Approved   bool     `json:"approved"`
	Banned     bool     `json:"banned"`
	Roles      []string `json:"roles"`
}

func (q *Queries) GetSafeUserById(ctx context.Context, id int64) (GetSafeUserByIdRow, error) {
	row := q.db.QueryRow(ctx, getSafeUserById, id)
	var i GetSafeUserByIdRow
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.Roles,
	)
	return i, err
}

const getUserById = `-- name: GetUserById :one
SELECT
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM
    users
WHERE
    id = $1
    AND deleted_at IS NULL
LIMIT
    1
`

func (q *Queries) GetUserById(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserById, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserByIdentifier = `-- name: GetUserByIdentifier :one
SELECT
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
FROM
    users
WHERE
    identifier = $1
    AND deleted_at IS NULL
LIMIT
    1
`

func (q *Queries) GetUserByIdentifier(ctx context.Context, identifier string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByIdentifier, identifier)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateUser = `-- name: UpdateUser :one
UPDATE users
SET
    (
        approved,
        banned,
        meta_data,
        roles,
        expires_at,
        updated_at
    ) = (
        $1,
        $2,
        $3,
        $4,
        $5,
        CURRENT_TIMESTAMP
    )
WHERE
    id = $6
RETURNING
    id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
`

type UpdateUserParams struct {
	Approved  bool               `json:"approved"`
	Banned    bool               `json:"banned"`
	MetaData  []byte             `json:"meta_data"`
	Roles     []string           `json:"roles"`
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	ID        int64              `json:"id"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUser,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.Roles,
		arg.ExpiresAt,
		arg.ID,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.Roles,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
