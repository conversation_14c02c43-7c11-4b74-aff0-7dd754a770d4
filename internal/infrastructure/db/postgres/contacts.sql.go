// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: contacts.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const createContact = `-- name: <PERSON>reate<PERSON>ontact :one
INSERT INTO contacts (
        identifier,
        contact_type,
        user_id,
        mobile,
        email,
        meta_data
    )
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
`

type CreateContactParams struct {
	Identifier  string      `json:"identifier"`
	ContactType ContactType `json:"contact_type"`
	UserID      int64       `json:"user_id"`
	Mobile      string      `json:"mobile"`
	Email       string      `json:"email"`
	MetaData    []byte      `json:"meta_data"`
}

func (q *Queries) CreateContact(ctx context.Context, arg CreateContactParams) (Contact, error) {
	row := q.db.QueryRow(ctx, createContact,
		arg.Identifier,
		arg.ContactType,
		arg.UserID,
		arg.Mobile,
		arg.Email,
		arg.MetaData,
	)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const findActiveEmailContact = `-- name: FindActiveEmailContact :one
SELECT 
    c.id, c.identifier, c.contact_type, c.user_id, c.mobile, c.mobile_totp, c.is_mobile_verified, c.mobile_totp_expires_at, c.email, c.email_totp, c.is_email_verified, c.email_totp_expires_at, c.meta_data, c.created_at, c.updated_at, c.deleted_at,
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.roles as user_roles,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM contacts c
JOIN users u ON u.id = c.user_id
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
WHERE 
    c.email = $1
    AND c.contact_type = 'EMAIL'
    AND c.is_email_verified = TRUE
    AND c.deleted_at IS NULL
    AND u.deleted_at IS NULL
    AND u.banned = FALSE
    AND $2 = ANY(u.roles)
LIMIT 1
`

type FindActiveEmailContactParams struct {
	Email string   `json:"email"`
	Roles []string `json:"roles"`
}

type FindActiveEmailContactRow struct {
	ID                  int64              `json:"id"`
	Identifier          string             `json:"identifier"`
	ContactType         ContactType        `json:"contact_type"`
	UserID              int64              `json:"user_id"`
	Mobile              string             `json:"mobile"`
	MobileTotp          pgtype.Text        `json:"mobile_totp"`
	IsMobileVerified    bool               `json:"is_mobile_verified"`
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	Email               string             `json:"email"`
	EmailTotp           pgtype.Text        `json:"email_totp"`
	IsEmailVerified     bool               `json:"is_email_verified"`
	EmailTotpExpiresAt  pgtype.Timestamptz `json:"email_totp_expires_at"`
	MetaData            []byte             `json:"meta_data"`
	CreatedAt           time.Time          `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	DeletedAt           pgtype.Timestamptz `json:"deleted_at"`
	UserID_2            int64              `json:"user_id_2"`
	UserIdentifier      string             `json:"user_identifier"`
	UserApproved        bool               `json:"user_approved"`
	UserBanned          bool               `json:"user_banned"`
	UserRoles           []string           `json:"user_roles"`
	ProfileID           pgtype.Int8        `json:"profile_id"`
	ProfileIdentifier   pgtype.Text        `json:"profile_identifier"`
	ProfileType         NullProfileType    `json:"profile_type"`
	ProfileFirstName    pgtype.Text        `json:"profile_first_name"`
	ProfileLastName     pgtype.Text        `json:"profile_last_name"`
	ProfileNationalID   pgtype.Text        `json:"profile_national_id"`
	ProfileStatus       NullProfileStatus  `json:"profile_status"`
	ProfileMetaData     []byte             `json:"profile_meta_data"`
	ProfileCreatedAt    pgtype.Timestamptz `json:"profile_created_at"`
}

func (q *Queries) FindActiveEmailContact(ctx context.Context, arg FindActiveEmailContactParams) (FindActiveEmailContactRow, error) {
	row := q.db.QueryRow(ctx, findActiveEmailContact, arg.Email, arg.Roles)
	var i FindActiveEmailContactRow
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.UserID_2,
		&i.UserIdentifier,
		&i.UserApproved,
		&i.UserBanned,
		&i.UserRoles,
		&i.ProfileID,
		&i.ProfileIdentifier,
		&i.ProfileType,
		&i.ProfileFirstName,
		&i.ProfileLastName,
		&i.ProfileNationalID,
		&i.ProfileStatus,
		&i.ProfileMetaData,
		&i.ProfileCreatedAt,
	)
	return i, err
}

const findActiveMobileContact = `-- name: FindActiveMobileContact :one
SELECT 
    c.id, c.identifier, c.contact_type, c.user_id, c.mobile, c.mobile_totp, c.is_mobile_verified, c.mobile_totp_expires_at, c.email, c.email_totp, c.is_email_verified, c.email_totp_expires_at, c.meta_data, c.created_at, c.updated_at, c.deleted_at,
    u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.roles as user_roles,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM contacts c
JOIN users u ON u.id = c.user_id
LEFT JOIN profiles p ON p.user_id = u.id AND p.deleted_at IS NULL
WHERE 
    c.mobile = $1
    AND c.contact_type = 'MOBILE'
    AND c.deleted_at IS NULL
    AND u.deleted_at IS NULL
    AND u.banned = FALSE
    AND u.roles @> $2::text[]
LIMIT 1
`

type FindActiveMobileContactParams struct {
	Mobile string   `json:"mobile"`
	Roles  []string `json:"roles"`
}

type FindActiveMobileContactRow struct {
	ID                  int64              `json:"id"`
	Identifier          string             `json:"identifier"`
	ContactType         ContactType        `json:"contact_type"`
	UserID              int64              `json:"user_id"`
	Mobile              string             `json:"mobile"`
	MobileTotp          pgtype.Text        `json:"mobile_totp"`
	IsMobileVerified    bool               `json:"is_mobile_verified"`
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	Email               string             `json:"email"`
	EmailTotp           pgtype.Text        `json:"email_totp"`
	IsEmailVerified     bool               `json:"is_email_verified"`
	EmailTotpExpiresAt  pgtype.Timestamptz `json:"email_totp_expires_at"`
	MetaData            []byte             `json:"meta_data"`
	CreatedAt           time.Time          `json:"created_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	DeletedAt           pgtype.Timestamptz `json:"deleted_at"`
	UserID_2            int64              `json:"user_id_2"`
	UserIdentifier      string             `json:"user_identifier"`
	UserApproved        bool               `json:"user_approved"`
	UserBanned          bool               `json:"user_banned"`
	UserRoles           []string           `json:"user_roles"`
	ProfileID           pgtype.Int8        `json:"profile_id"`
	ProfileIdentifier   pgtype.Text        `json:"profile_identifier"`
	ProfileType         NullProfileType    `json:"profile_type"`
	ProfileFirstName    pgtype.Text        `json:"profile_first_name"`
	ProfileLastName     pgtype.Text        `json:"profile_last_name"`
	ProfileNationalID   pgtype.Text        `json:"profile_national_id"`
	ProfileStatus       NullProfileStatus  `json:"profile_status"`
	ProfileMetaData     []byte             `json:"profile_meta_data"`
	ProfileCreatedAt    pgtype.Timestamptz `json:"profile_created_at"`
}

func (q *Queries) FindActiveMobileContact(ctx context.Context, arg FindActiveMobileContactParams) (FindActiveMobileContactRow, error) {
	row := q.db.QueryRow(ctx, findActiveMobileContact, arg.Mobile, arg.Roles)
	var i FindActiveMobileContactRow
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.UserID_2,
		&i.UserIdentifier,
		&i.UserApproved,
		&i.UserBanned,
		&i.UserRoles,
		&i.ProfileID,
		&i.ProfileIdentifier,
		&i.ProfileType,
		&i.ProfileFirstName,
		&i.ProfileLastName,
		&i.ProfileNationalID,
		&i.ProfileStatus,
		&i.ProfileMetaData,
		&i.ProfileCreatedAt,
	)
	return i, err
}

const getContactByEmail = `-- name: GetContactByEmail :one
SELECT id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
FROM contacts
WHERE email = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetContactByEmail(ctx context.Context, email string) (Contact, error) {
	row := q.db.QueryRow(ctx, getContactByEmail, email)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getContactById = `-- name: GetContactById :one
SELECT id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
FROM contacts
WHERE id = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetContactById(ctx context.Context, id int64) (Contact, error) {
	row := q.db.QueryRow(ctx, getContactById, id)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getContactByMobile = `-- name: GetContactByMobile :one
SELECT id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
FROM contacts
WHERE mobile = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetContactByMobile(ctx context.Context, mobile string) (Contact, error) {
	row := q.db.QueryRow(ctx, getContactByMobile, mobile)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getContactByUserID = `-- name: GetContactByUserID :one
SELECT id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
FROM contacts
WHERE user_id = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetContactByUserID(ctx context.Context, userID int64) (Contact, error) {
	row := q.db.QueryRow(ctx, getContactByUserID, userID)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getContactByUserId = `-- name: GetContactByUserId :one
SELECT id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
FROM contacts
WHERE user_id = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetContactByUserId(ctx context.Context, userID int64) (Contact, error) {
	row := q.db.QueryRow(ctx, getContactByUserId, userID)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const safeDeleteContact = `-- name: SafeDeleteContact :exec
UPDATE contacts
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) SafeDeleteContact(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, safeDeleteContact, id)
	return err
}

const setEmailOTP = `-- name: SetEmailOTP :one
UPDATE contacts
SET email_totp = $1, email_totp_expires_at = $2
WHERE id = $3
RETURNING id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
`

type SetEmailOTPParams struct {
	EmailTotp          pgtype.Text        `json:"email_totp"`
	EmailTotpExpiresAt pgtype.Timestamptz `json:"email_totp_expires_at"`
	ID                 int64              `json:"id"`
}

func (q *Queries) SetEmailOTP(ctx context.Context, arg SetEmailOTPParams) (Contact, error) {
	row := q.db.QueryRow(ctx, setEmailOTP, arg.EmailTotp, arg.EmailTotpExpiresAt, arg.ID)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const setMobileOTP = `-- name: SetMobileOTP :one
UPDATE contacts
SET mobile_totp = $1, mobile_totp_expires_at = $2
WHERE id = $3
RETURNING id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
`

type SetMobileOTPParams struct {
	MobileTotp          pgtype.Text        `json:"mobile_totp"`
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	ID                  int64              `json:"id"`
}

func (q *Queries) SetMobileOTP(ctx context.Context, arg SetMobileOTPParams) (Contact, error) {
	row := q.db.QueryRow(ctx, setMobileOTP, arg.MobileTotp, arg.MobileTotpExpiresAt, arg.ID)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateContact = `-- name: UpdateContact :one
UPDATE contacts
SET (
        mobile,
        email,
        meta_data,
        mobile_totp,
        mobile_totp_expires_at,
        email_totp,
        email_totp_expires_at,
        updated_at
    ) = ($1, $2, $3, $4, $5, $6, $7, $8)
WHERE id = $9
RETURNING id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
`

type UpdateContactParams struct {
	Mobile              string             `json:"mobile"`
	Email               string             `json:"email"`
	MetaData            []byte             `json:"meta_data"`
	MobileTotp          pgtype.Text        `json:"mobile_totp"`
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	EmailTotp           pgtype.Text        `json:"email_totp"`
	EmailTotpExpiresAt  pgtype.Timestamptz `json:"email_totp_expires_at"`
	UpdatedAt           pgtype.Timestamptz `json:"updated_at"`
	ID                  int64              `json:"id"`
}

func (q *Queries) UpdateContact(ctx context.Context, arg UpdateContactParams) (Contact, error) {
	row := q.db.QueryRow(ctx, updateContact,
		arg.Mobile,
		arg.Email,
		arg.MetaData,
		arg.MobileTotp,
		arg.MobileTotpExpiresAt,
		arg.EmailTotp,
		arg.EmailTotpExpiresAt,
		arg.UpdatedAt,
		arg.ID,
	)
	var i Contact
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.ContactType,
		&i.UserID,
		&i.Mobile,
		&i.MobileTotp,
		&i.IsMobileVerified,
		&i.MobileTotpExpiresAt,
		&i.Email,
		&i.EmailTotp,
		&i.IsEmailVerified,
		&i.EmailTotpExpiresAt,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
