// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: profile.sql

package postgres

import (
	"context"
)

const getProfileByUserID = `-- name: GetProfileByUserID :one
SELECT id, identifier, user_id, profile_type, first_name, last_name, national_id, status, meta_data, created_at, updated_at, deleted_at
FROM profiles
WHERE user_id = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetProfileByUserID(ctx context.Context, userID int64) (Profile, error) {
	row := q.db.QueryRow(ctx, getProfileByUserID, userID)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserID,
		&i.ProfileType,
		&i.FirstName,
		&i.LastName,
		&i.NationalID,
		&i.Status,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Deleted<PERSON>t,
	)
	return i, err
}
