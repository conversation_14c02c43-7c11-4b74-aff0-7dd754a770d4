package user

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/nats-io/nats.go"
)

// MeshService implements servicemesh.UserMeshService.
type userMeshService struct {
	repo          postgres.Store
	nats          *nats.Conn
	app           string
	subscriptions map[string]*nats.Subscription
}

// NewMeshService creates a new user mesh service.
func NewMeshService(opts *UserServiceOpts) servicemesh.UserMeshService {
	return &userMeshService{
		repo:          opts.Repository,
		nats:          opts.NATS,
		app:           opts.ApplicationName,
		subscriptions: make(map[string]*nats.Subscription),
	}
}

// Start implements servicemesh.UserMeshService.
func (u *userMeshService) Start(ctx context.Context, subjects []string) error {
	for _, subject := range subjects {
		sub, err := u.nats.QueueSubscribe(subject, u.app, func(msg *nats.Msg) {
			fmt.Println("mesh service message arrived", string(msg.Data))
			message := &servicemesh.UserMeshServiceMessage{}
			if err := json.Unmarshal(msg.Data, message); err != nil {
				u.nats.Publish(msg.Reply, []byte("error: invalid message format"))
				return
			}

			switch msg.Subject {
			case servicemesh.USER_SERVICE_GET_SAFE_USER_BY_ID:
				if message.UserID == 0 {
					data, err := json.Marshal(servicemesh.UserModel{})
					if err != nil {
						msg.Respond([]byte("error: failed to marshal empty user info"))
						return
					}
					msg.Respond(data)
				}

				user, err := u.GetSafeUserByID(ctx, message.UserID)
				if err != nil {
					data, err := json.Marshal(servicemesh.UserModel{})
					if err != nil {
						msg.Respond([]byte("error: failed to marshal empty user info"))
						return
					}
					msg.Respond(data)
				}
				data, err := json.Marshal(user)
				if err != nil {
					msg.Respond([]byte("error: failed to marshal user info"))
					return
				}
				if err := msg.Respond(data); err != nil {
					return
				}
			case servicemesh.USER_SERVICE_GET_SAFE_USER_BY_IDENTIFIER:
				// Handle GetSafeUserByIdentifier
				if message.Identifier == "" {
					data, err := json.Marshal(servicemesh.UserModel{})
					if err != nil {
						msg.Respond([]byte("error: failed to marshal empty user info"))
						return
					}
					msg.Respond(data)
				}

				user, err := u.GetSafeUserByIdentifier(ctx, message.Identifier)
				if err != nil {
					data, err := json.Marshal(servicemesh.UserModel{})
					if err != nil {
						msg.Respond([]byte("error: failed to marshal empty user info"))
						return
					}
					msg.Respond(data)
				}

				data, err := json.Marshal(user)
				if err != nil {
					msg.Respond([]byte("error: failed to marshal user info"))
					return
				}
				if err := msg.Respond(data); err != nil {
					return
				}
			}
		})

		if err != nil {
			return err
		}

		u.subscriptions[subject] = sub
	}
	return nil
}

// Stop implements servicemesh.UserMeshService.
func (u *userMeshService) Stop(ctx context.Context) error {
	for subject, sub := range u.subscriptions {
		if err := sub.Unsubscribe(); err != nil {
			return err
		}
		delete(u.subscriptions, subject)
	}
	return nil
}

// GetSafeUserByID implements servicemesh.UserMeshService.
func (u *userMeshService) GetSafeUserByID(ctx context.Context, userID int64) (*servicemesh.UserModel, error) {
	user, err := u.repo.GetUserById(ctx, userID)
	if err != nil {
		return nil, err
	}

	profile, err := u.repo.GetProfileByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	contact, err := u.repo.GetContactByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	return &servicemesh.UserModel{
		ID:         user.ID,
		Identifier: user.Identifier,
		Approved:   user.Approved,
		Banned:     user.Banned,
		MetaData:   user.MetaData,
		Roles:      user.Roles,
		ExpiresAt:  &user.ExpiresAt.Time,
		CreatedAt:  user.CreatedAt,
		UpdatedAt:  &user.UpdatedAt.Time,
		DeletedAt:  &user.DeletedAt.Time,
		Profile: servicemesh.ProfileModel{
			ID:          profile.ID,
			Identifier:  profile.Identifier,
			UserID:      profile.UserID,
			ProfileType: string(profile.ProfileType),
			FirstName:   profile.FirstName,
			LastName:    profile.LastName,
			NationalID:  profile.NationalID,
			Status:      string(profile.Status),
			MetaData:    profile.MetaData,
			CreatedAt:   profile.CreatedAt,
			UpdatedAt:   &profile.UpdatedAt.Time,
			DeletedAt:   &profile.DeletedAt.Time,
		},
		Contact: servicemesh.ContactModel{
			ID:                  contact.ID,
			Identifier:          contact.Identifier,
			ContactType:         string(contact.ContactType),
			UserID:              contact.UserID,
			Mobile:              contact.Mobile,
			MobileTotp:          contact.MobileTotp.String,
			IsMobileVerified:    contact.IsMobileVerified,
			MobileTotpExpiresAt: &contact.MobileTotpExpiresAt.Time,
			Email:               contact.Email,
			EmailTotp:           contact.EmailTotp.String,
			IsEmailVerified:     contact.IsEmailVerified,
			EmailTotpExpiresAt:  &contact.EmailTotpExpiresAt.Time,
			MetaData:            contact.MetaData,
			CreatedAt:           contact.CreatedAt,
			UpdatedAt:           &contact.UpdatedAt.Time,
			DeletedAt:           &contact.DeletedAt.Time,
		},
	}, nil
}

// GetSafeUserByIdentifier implements servicemesh.UserMeshService.
func (u *userMeshService) GetSafeUserByIdentifier(ctx context.Context, identifier string) (*servicemesh.UserModel, error) {
	user, err := u.repo.GetUserByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}

	profile, err := u.repo.GetProfileByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	contact, err := u.repo.GetContactByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	return &servicemesh.UserModel{
		ID:         user.ID,
		Identifier: user.Identifier,
		Approved:   user.Approved,
		Banned:     user.Banned,
		MetaData:   user.MetaData,
		Roles:      user.Roles,
		ExpiresAt:  &user.ExpiresAt.Time,
		CreatedAt:  user.CreatedAt,
		UpdatedAt:  &user.UpdatedAt.Time,
		DeletedAt:  &user.DeletedAt.Time,
		Profile: servicemesh.ProfileModel{
			ID:          profile.ID,
			Identifier:  profile.Identifier,
			UserID:      profile.UserID,
			ProfileType: string(profile.ProfileType),
			FirstName:   profile.FirstName,
			LastName:    profile.LastName,
			NationalID:  profile.NationalID,
			Status:      string(profile.Status),
			MetaData:    profile.MetaData,
			CreatedAt:   profile.CreatedAt,
			UpdatedAt:   &profile.UpdatedAt.Time,
			DeletedAt:   &profile.DeletedAt.Time,
		},
		Contact: servicemesh.ContactModel{
			ID:                  contact.ID,
			Identifier:          contact.Identifier,
			ContactType:         string(contact.ContactType),
			UserID:              contact.UserID,
			Mobile:              contact.Mobile,
			MobileTotp:          contact.MobileTotp.String,
			IsMobileVerified:    contact.IsMobileVerified,
			MobileTotpExpiresAt: &contact.MobileTotpExpiresAt.Time,
			Email:               contact.Email,
			EmailTotp:           contact.EmailTotp.String,
			IsEmailVerified:     contact.IsEmailVerified,
			EmailTotpExpiresAt:  &contact.EmailTotpExpiresAt.Time,
			MetaData:            contact.MetaData,
			CreatedAt:           contact.CreatedAt,
			UpdatedAt:           &contact.UpdatedAt.Time,
			DeletedAt:           &contact.DeletedAt.Time,
		},
	}, nil
}
