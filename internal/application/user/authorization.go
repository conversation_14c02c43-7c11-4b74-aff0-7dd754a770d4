package user

import (
	"context"

	"github.com/liveutil/user_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedUserServiceServer

	next pb.UserServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.UserServiceServer
func NewAuthorizationMiddleware(service pb.UserServiceServer) pb.UserServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}

// ContextUser implements user_service.UserServiceServer.
func (a *authorizationMiddleware) ContextUser(ctx context.Context, req *pb.Empty) (*pb.ContextUserResponse, error) {
	return a.next.ContextUser(ctx, req)
}

// OtpVerify implements user_service.UserServiceServer.
func (a *authorizationMiddleware) OtpVerify(ctx context.Context, req *pb.OtpVerifyRequest) (*pb.AuthenticationResponse, error) {
	return a.next.OtpVerify(ctx, req)
}

// RefreshToken implements user_service.UserServiceServer.
func (a *authorizationMiddleware) RefreshToken(ctx context.Context, req *pb.RefreshTokenRequest) (*pb.AuthenticationResponse, error) {
	return a.next.RefreshToken(ctx, req)
}

// SignIn implements user_service.UserServiceServer.
func (a *authorizationMiddleware) SignIn(ctx context.Context, req *pb.SignInRequest) (*pb.AuthorizationResponse, error) {
	return a.next.SignIn(ctx, req)
}

// SignUp implements user_service.UserServiceServer.
func (a *authorizationMiddleware) SignUp(ctx context.Context, req *pb.SignUpUserWithDefaultsRequest) (*pb.AuthorizationResponse, error) {
	return a.next.SignUp(ctx, req)
}
