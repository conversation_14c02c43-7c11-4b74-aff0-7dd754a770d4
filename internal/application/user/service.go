package user

import (
	"context"

	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/go-lib/totp"
	"github.com/liveutil/user_service/internal/config"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type service struct {
	pb.UnimplementedUserServiceServer

	repo         postgres.Store
	paseto       paseto.Maker
	config       *config.Configuration
	redis        *redis.Client
	nats         *nats.Conn
	logger       kitlog.Logger
	totp_config  totp.TOTPServiceConfig
	totp_service totp.TOTPService
	cards        servicemesh.CardMeshService
}

func NewService(opts *UserServiceOpts) pb.UserServiceServer {
	totpConfig := totp.TOTPServiceConfig{
		Issuer:    opts.Config.Issuer,
		Digits:    6,
		Period:    300,
		Algorithm: otp.AlgorithmSHA1,
		Skew:      1,
	}

	totpService := totp.NewRedisTOTPService(opts.Redis, opts.ApplicationName, totpConfig)

	return &service{
		repo:         opts.Repository,
		config:       opts.Config,
		redis:        opts.Redis,
		paseto:       opts.PASETO,
		nats:         opts.NATS,
		totp_config:  totpConfig,
		totp_service: totpService,
		logger:       opts.Logger,
		cards:        opts.CardsMeshService,
	}
}

// ContextUser implements user_service.UserServiceServer.
func (s *service) ContextUser(ctx context.Context, req *pb.Empty) (*pb.ContextUserResponse, error) {
	user := postgres.User{}
	err := contextutil.CatchUser(ctx, &user)
	if err != nil {
		return nil, err
	}

	user, err = s.repo.GetUserById(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	contact, err := s.repo.GetContactByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	profile, err := s.repo.GetProfileByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	// Create response with debug logging
	return &pb.ContextUserResponse{
		User: &pb.User{
			Identifier: user.Identifier,
			Approved:   user.Approved,
			Banned:     user.Banned,
			Roles:      user.Roles,
			CreatedAt:  timestamppb.New(user.CreatedAt),
			Profile: &pb.Profile{
				Identifier:  profile.Identifier,
				FirstName:   profile.FirstName,
				LastName:    profile.LastName,
				NationalId:  profile.NationalID,
				ProfileType: string(profile.ProfileType),
				Status:      string(profile.Status),
				MetaData:    string(profile.MetaData),
				CreatedAt:   timestamppb.New(profile.CreatedAt),
			},
			Contact: &pb.Contact{
				Identifier: contact.Identifier,
				Mobile:     contact.Mobile,
				Email:      contact.Email,
				MetaData:   string(contact.MetaData),
				CreatedAt:  timestamppb.New(contact.CreatedAt),
			},
		},
	}, nil
}
