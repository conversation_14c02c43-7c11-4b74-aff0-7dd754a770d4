package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/env"
	"github.com/liveutil/go-lib/errs"
	"github.com/liveutil/go-lib/pgutil"
	"github.com/liveutil/go-lib/worker"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"github.com/mssola/user_agent"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// SignIn implements user_service.UserServiceServer.
func (s *service) SignIn(ctx context.Context, req *pb.SignInRequest) (*pb.AuthorizationResponse, error) {

	contactValue := req.Mobile
	var contactType postgres.ContactType = postgres.ContactTypeMOBILE

	if len(contactValue) == 0 {
		contactValue = req.Email
	}

	payload := &worker.PayloadSendVerificationCode{}

	var contactID int64 = 0

	// check if mobile/email code is not expired
	switch contactType {
	case postgres.ContactTypeMOBILE:
		contact, err := s.repo.FindActiveMobileContact(ctx, postgres.FindActiveMobileContactParams{
			Mobile: contactValue,
			Roles:  []string{req.Role},
		})
		if err != nil {
			return nil, err
		}

		if contact.MobileTotpExpiresAt.Time.After(time.Now().UTC()) {
			return &pb.AuthorizationResponse{
				Error:     true,
				Message:   errs.ErrTooManyLoginRequest.Error(),
				ExpiredAt: timestamppb.New(contact.MobileTotpExpiresAt.Time),
			}, errs.ErrTooManyLoginRequest
		}

		// notification fill payload
		payload.Mobile = contact.Mobile
		payload.Username = contact.ProfileFirstName.String
		payload.Email = contact.Email

		// fill contact id
		contactID = contact.ID
	case postgres.ContactTypeEMAIL:
		contact, err := s.repo.FindActiveEmailContact(ctx, postgres.FindActiveEmailContactParams{
			Email: contactValue,
			Roles: []string{req.Role},
		})
		if err != nil {
			return nil, err
		}

		if contact.EmailTotpExpiresAt.Time.After(time.Now().UTC()) {
			return &pb.AuthorizationResponse{
				Error:     true,
				Message:   errs.ErrTooManyLoginRequest.Error(),
				ExpiredAt: timestamppb.New(contact.EmailTotpExpiresAt.Time),
			}, errs.ErrTooManyLoginRequest
		}

		// notification fill payload
		payload.Email = contact.Email
		payload.Username = contact.ProfileFirstName.String
		payload.Mobile = contact.Mobile

		// fill contact id
		contactID = contact.ID
	}

	// generate totp code and define its expire date
	totpGenCtx := context.Background()
	expire := time.Now().UTC().Add(time.Duration(s.config.VerificationDuration) * time.Minute).UTC()
	var err error

	// write raw totp code to notification payload
	payload.VerificationCode, err = s.totp_service.GenerateTOTP(totpGenCtx, contactValue, time.Duration(s.config.VerificationDuration)*time.Minute)
	if err != nil {
		return nil, err
	}

	// encrypt totp code to store in database
	encryptedTOTP, err := bcrypt.GenerateFromPassword([]byte(payload.VerificationCode), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// store encrypted totp code in database
	switch contactType {
	case postgres.ContactTypeMOBILE:
		_, err = s.repo.SetMobileOTP(ctx, postgres.SetMobileOTPParams{
			ID:                  contactID,
			MobileTotp:          pgutil.GetPGText(string(encryptedTOTP)),
			MobileTotpExpiresAt: pgutil.GetPGTimestamptz(expire),
		})
		if err != nil {
			return nil, err
		}

	case postgres.ContactTypeEMAIL:
		_, err = s.repo.SetEmailOTP(ctx, postgres.SetEmailOTPParams{
			ID:                 contactID,
			EmailTotp:          pgutil.GetPGText(string(encryptedTOTP)),
			EmailTotpExpiresAt: pgutil.GetPGTimestamptz(expire),
		})
		if err != nil {
			return nil, err
		}
	}

	// send verification code to user via NATS message queue
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	// define subject for notification
	// @ToDo: add notification topic pattern to notification service docs
	// example: service.notification.v1.mobile.SUPER_ADMIN
	// example: service.notification.v1.email.USER
	subject := fmt.Sprintf("%s.%s.%s", s.config.NotificationTopic, contactType, req.Role)

	err = s.nats.Publish(subject, jsonPayload)
	if err != nil {
		return nil, err
	}

	// provide meta for non-production environment that contains raw totp code
	if !env.IsProduction() {
		agent := user_agent.New(contextutil.UserAgent(ctx))
		browser, version := agent.Browser()
		meta := fmt.Sprintf("%s, %s - %s, %s, verification code: %s",
			agent.OS(),
			browser,
			version,
			contextutil.RemoteIP(ctx),
			payload.VerificationCode,
		)

		return &pb.AuthorizationResponse{
			Error:     false,
			Message:   meta,
			ExpiredAt: timestamppb.New(expire),
		}, nil
	}

	return &pb.AuthorizationResponse{
		Error:     false,
		Message:   "verification code sent successfully",
		ExpiredAt: timestamppb.New(expire),
	}, nil
}
