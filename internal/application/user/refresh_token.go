package user

import (
	"context"
	"strconv"
	"time"

	"github.com/liveutil/go-lib/errs"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// RefreshToken implements user_service.UserServiceServer.
func (s *service) RefreshToken(ctx context.Context, req *pb.RefreshTokenRequest) (*pb.AuthenticationResponse, error) {
	// verify refresh token
	tokenMaker, err := paseto.NewPasetoMaker(s.config.TokenSymmetricKey, s.config.Issuer, s.config.Audience)
	if err != nil {
		return nil, err
	}

	// parse token payload claims
	payload, err := tokenMaker.VerifyToken(req.RefreshToken)
	if err != nil {
		return nil, err
	}

	// check if token is expired
	if len(payload.JTI) == 0 || payload.ExpiresAt.Before(time.Now().UTC()) {
		return nil, errs.ErrAuthorizationFailed
	}

	// parse session id from token payload
	sessionID, err := strconv.ParseInt(payload.JTI, 10, 64)
	if err != nil {
		return nil, err
	}

	userID, err := strconv.ParseInt(payload.Subject, 10, 64)
	if err != nil {
		return nil, err
	}

	// get user by id from token payload to check if user is banned or not found
	user, err := s.repo.GetUserById(ctx, userID)
	if err != nil {
		return nil, errs.ErrUserBannedOrNotFount
	}

	// check if user is banned
	if user.Banned {
		return nil, errs.ErrUserBannedOrNotFount
	}

	// get session by id and user id to check if session is expired
	session, err := s.repo.GetSessionById(ctx, postgres.GetSessionByIdParams{
		ID:     sessionID,
		UserID: userID,
	})
	if err != nil {
		return nil, errs.ErrAuthorizationFailed
	}

	if session.ExpiresIn.Before(time.Now().UTC()) {
		return nil, errs.ErrSessionNotFoundOrExpired
	}

	// create token claims
	tokenClaims := &paseto.TokenClaims{
		Subject:   strconv.FormatInt(userID, 10),
		JTI:       "",
		Roles:     payload.Roles,
		Issuer:    s.config.Issuer,
		Audience:  s.config.Audience,
		IssuedAt:  time.Now().UTC(),
		ExpiresAt: time.Now().UTC().Add(time.Duration(s.config.AccessTokenDuration) * time.Minute),
	}

	// create refresh token claims
	refreshClaims := &paseto.TokenClaims{
		Subject:   strconv.FormatInt(userID, 10),
		JTI:       strconv.FormatInt(sessionID, 10),
		Roles:     payload.Roles,
		Issuer:    s.config.Issuer,
		Audience:  s.config.Audience,
		IssuedAt:  time.Now().UTC(),
		ExpiresAt: time.Now().UTC().Add(time.Duration(s.config.RefreshTokenDuration) * time.Minute),
	}

	// prepare params to renew session in database
	update := postgres.RenewUserSessionParams{
		UserID:    session.UserID,
		ID:        sessionID,
		ExpiresIn: tokenClaims.ExpiresAt,
	}

	// renew session in database
	session, err = s.repo.RenewUserSession(ctx, update)
	if err != nil {
		return nil, err
	}

	// encode token claims to encrypted token string
	token, err := tokenMaker.EncodeClaims(tokenClaims)
	if err != nil {
		return nil, err
	}

	// encode refresh token claims to encrypted token string
	refreshToken, err := tokenMaker.EncodeClaims(refreshClaims)
	if err != nil {
		return nil, err
	}

	// return response
	return &pb.AuthenticationResponse{
		Error:                 false,
		Message:               "token refreshed successfully",
		AccessToken:           token,
		RefreshToken:          refreshToken,
		AccessTokenExpiredAt:  timestamppb.New(tokenClaims.ExpiresAt),
		RefreshTokenExpiredAt: timestamppb.New(refreshClaims.ExpiresAt),
	}, nil
}
