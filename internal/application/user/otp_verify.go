package user

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/pgutil"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"github.com/oklog/ulid/v2"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// OtpVerify implements user_service.UserServiceServer.
func (s *service) OtpVerify(ctx context.Context, req *pb.OtpVerifyRequest) (*pb.AuthenticationResponse, error) {
	// determine contact type by email or mobile
	contactType := postgres.ContactTypeMOBILE
	if len(req.Email) > 0 {
		contactType = postgres.ContactTypeEMAIL
	}

	var contact postgres.Contact
	var err error

	switch contactType {
	case postgres.ContactTypeMOBILE:
		contact, err = s.repo.GetContactByMobile(ctx, req.Mobile)
		if err != nil {
			return nil, err
		}
	case postgres.ContactTypeEMAIL:
		contact, err = s.repo.GetContactByEmail(ctx, req.Email)
		if err != nil {
			return nil, err
		}
	}

	// check if related contact user is banned or not
	user, err := s.repo.GetUserById(ctx, contact.UserID)
	if err != nil {
		return nil, err
	}
	if user.Banned {
		return nil, errors.New("user is banned")
	}

	// check if related contact user is deleted or not
	if user.ExpiresAt.Valid && user.ExpiresAt.Time.Before(time.Now().UTC()) {
		return nil, errors.New("user is expired")
	}

	// check if totp code is expired or not
	switch contactType {
	case postgres.ContactTypeMOBILE:
		if contact.MobileTotpExpiresAt.Valid && contact.MobileTotpExpiresAt.Time.Before(time.Now().UTC()) {
			return nil, errors.New("mobile totp code is expired")
		}
	case postgres.ContactTypeEMAIL:
		if contact.EmailTotpExpiresAt.Valid && contact.EmailTotpExpiresAt.Time.Before(time.Now().UTC()) {
			return nil, errors.New("email totp code is expired")
		}
	}

	// if authorization mode sets to JWT by configuration, then create token bundles and return it
	session, err := s.repo.CreateUserSession(ctx, postgres.CreateUserSessionParams{
		Identifier:   ulid.Make().String(),
		UserID:       user.ID,
		ExpiresIn:    time.Now().UTC().Add(time.Duration(s.config.RefreshTokenDuration) * time.Minute),
		Notification: pgutil.GetPGText(""),
	})
	if err != nil {
		return nil, err
	}

	tokenMaker, err := paseto.NewPasetoMaker(s.config.TokenSymmetricKey, s.config.Issuer, s.config.Audience)
	if err != nil {
		return nil, err
	}

	tokenClaims := &paseto.TokenClaims{
		Subject:   strconv.FormatInt(user.ID, 10),
		Roles:     user.Roles,
		Issuer:    s.config.Issuer,
		Audience:  s.config.Audience,
		IssuedAt:  time.Unix(session.CreatedAt.UTC().Unix(), 0),
		ExpiresAt: time.Now().UTC().Add(time.Duration(s.config.AccessTokenDuration) * time.Minute),
		ClientID:  uuid.NewString(),
	}

	refreshClaims := &paseto.TokenClaims{
		Subject:   strconv.FormatInt(user.ID, 10),
		Roles:     user.Roles,
		Issuer:    s.config.Issuer,
		Audience:  s.config.Audience,
		IssuedAt:  time.Unix(session.CreatedAt.UTC().Unix(), 0),
		ExpiresAt: time.Now().UTC().Add(time.Duration(s.config.RefreshTokenDuration) * time.Minute),
		JTI:       strconv.FormatInt(session.ID, 10),
	}

	tokenStr, err := tokenMaker.EncodeClaims(tokenClaims)
	if err != nil {
		return nil, err
	}

	refreshStr, err := tokenMaker.EncodeClaims(refreshClaims)
	if err != nil {
		return nil, err
	}

	_, err = s.repo.UpdateContact(ctx, postgres.UpdateContactParams{
		ID:                  contact.ID,
		Mobile:              contact.Mobile,
		Email:               contact.Email,
		MetaData:            contact.MetaData,
		MobileTotp:          pgutil.GetPGText(""),
		MobileTotpExpiresAt: pgutil.GetPGTimestamptz(time.Now().UTC()),
		EmailTotp:           pgutil.GetPGText(""),
		EmailTotpExpiresAt:  pgutil.GetPGTimestamptz(time.Now().UTC()),
		UpdatedAt:           pgutil.GetPGTimestamptz(time.Now().UTC()),
	})
	if err != nil {
		return nil, err
	}

	// cache user on redis
	go s.cachingUser(ctx, user)

	response := &pb.AuthenticationResponse{
		Error:                 false,
		Message:               "authentication successful",
		AccessToken:           tokenStr,
		AccessTokenExpiredAt:  timestamppb.New(tokenClaims.ExpiresAt),
		RefreshToken:          refreshStr,
		RefreshTokenExpiredAt: timestamppb.New(refreshClaims.ExpiresAt),
	}

	// Log response details for debugging
	defer func(begin time.Time) {
		_ = s.logger.Log(
			"method", "OtpVerify",
			"error", nil,
			"took", time.Since(begin),
			"response_size", len(response.String()),
			"access_token_expires_at", response.AccessTokenExpiredAt,
			"refresh_token_expires_at", response.RefreshTokenExpiredAt,
		)
	}(time.Now())

	return response, nil
}

func (s *service) cachingUser(ctx context.Context, user postgres.User) {
	data, err := json.Marshal(user)
	if err != nil {
		defer func(begin time.Time) {
			_ = s.logger.Log(
				"method", "cachingUser",
				"error", err.Error(),
				"took", time.Since(begin),
				"input", user,
			)
		}(time.Now())

		return
	}

	err = s.redis.Set(ctx, strconv.FormatInt(user.ID, 10), string(data), time.Hour*24).Err()
	if err != nil {
		defer func(begin time.Time) {
			_ = s.logger.Log(
				"method", "cachingUser",
				"error", err.Error(),
				"took", time.Since(begin),
				"input", user,
			)
		}(time.Now())

		return
	}

	defer func(begin time.Time) {
		_ = s.logger.Log(
			"method", "cachingUser",
			"error", nil,
			"took", time.Since(begin),
			"input", user,
		)
	}(time.Now())
}
