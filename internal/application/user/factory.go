package user

import (
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/user_service/internal/config"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
)

type UserServiceOpts struct {
	Repository      postgres.Store
	Config          *config.Configuration
	Redis           *redis.Client
	NATS            *nats.Conn
	Logger          kitlog.Logger
	PASETO          paseto.Maker
	SchemaPath      string
	ApplicationName string
}

// NewUserService creates a new user service with all middleware layers
func NewUserService(opts *UserServiceOpts) (pb.UserServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
