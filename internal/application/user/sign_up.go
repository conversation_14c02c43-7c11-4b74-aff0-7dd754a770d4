package user

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/env"
	"github.com/liveutil/go-lib/worker"
	"github.com/liveutil/user_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/user_service/pb"
	"github.com/mssola/user_agent"
	"github.com/oklog/ulid/v2"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var ErrUserAlreadyExists = fmt.Errorf("user already exists")

// SignUp implements user_service.UserServiceServer.
func (s *service) SignUp(ctx context.Context, req *pb.SignUpUserWithDefaultsRequest) (res *pb.AuthorizationResponse, err error) {
	contactType := postgres.ContactTypeMOBILE
	if len(req.Email) > 0 {
		contactType = postgres.ContactTypeEMAIL
	}

	params := postgres.CreateUserAndRelationsTxParams{
		CreateUserAndRelationsParams: postgres.CreateUserAndRelationsParams{
			Identifier:        ulid.Make().String(),
			ContactType:       contactType,
			ProfileType:       postgres.ProfileType(req.ProfileType),
			FirstName:         req.FirstName,
			LastName:          req.LastName,
			NationalID:        req.NationalId,
			ContactIdentifier: ulid.Make().String(),
			ProfileIdentifier: ulid.Make().String(),
		},
		// @ToDo: add after create hook (publish event, call webhook, ...)
		AfterCreate: func(user postgres.CreateUserAndRelationsRow) error {
			return nil
		},
	}

	payload := &worker.PayloadSendVerificationCode{}
	contactValue := req.Mobile

	switch contactType {
	case postgres.ContactTypeMOBILE:
		params.Mobile = req.Mobile
		// set email to identifier to avoid duplicate unique constraint violation
		params.Email = fmt.Sprintf("null@%s", params.Identifier)

		// notification fill payload
		payload.Mobile = params.Mobile
		payload.Username = params.FirstName

		contactValue = req.Mobile

	case postgres.ContactTypeEMAIL:
		params.Email = req.Email
		// set mobile to identifier to avoid duplicate unique constraint violation
		params.Mobile = fmt.Sprintf("null@%s", params.Identifier)

		// notification fill payload
		payload.Email = params.Email
		payload.Username = params.FirstName

		contactValue = req.Email
	}

	// generate totp code and define its expire date
	totpGenCtx := context.Background()
	expire := time.Now().UTC().Add(time.Duration(s.config.VerificationDuration) * time.Minute).UTC()

	// write raw totp code to notification payload
	payload.VerificationCode, err = s.totp_service.GenerateTOTP(totpGenCtx, contactValue, time.Duration(s.config.VerificationDuration)*time.Minute)
	if err != nil {
		return nil, err
	}

	// encrypt totp code to store in database
	encryptedTOTP, err := bcrypt.GenerateFromPassword([]byte(payload.VerificationCode), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// store encrypted totp code in create user and relations params
	switch contactType {
	case postgres.ContactTypeMOBILE:
		params.MobileTotp = pgtype.Text{String: string(encryptedTOTP), Valid: true}
		params.MobileTotpExpiresAt = pgtype.Timestamptz{Time: expire, Valid: true}
	case postgres.ContactTypeEMAIL:
		params.EmailTotp = pgtype.Text{String: string(encryptedTOTP), Valid: true}
		params.EmailTotpExpiresAt = pgtype.Timestamptz{Time: expire, Valid: true}
	}

	// create user and relations
	user, err := s.repo.CreateUserAndRelationsTx(ctx, params)
	if err != nil {
		// if error message contains (duplicate key value violates unique constraint ) then return error
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			return nil, status.Error(codes.AlreadyExists, "user already exists")
		}
		return nil, err
	}

	// send verification code to user via NATS message queue
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	// define subject for notification
	// @ToDo: add notification topic pattern to notification service docs
	// example: service.notification.v1.mobile.SUPER_ADMIN
	// example: service.notification.v1.email.USER
	subject := fmt.Sprintf("%s.%s.%s", s.config.NotificationTopic, contactType, user.UserRoles[0])

	err = s.nats.Publish(subject, jsonPayload)
	if err != nil {
		return nil, err
	}

	// provide meta for non-production environment that contains raw totp code
	if !env.IsProduction() {
		agent := user_agent.New(contextutil.UserAgent(ctx))
		browser, version := agent.Browser()
		meta := fmt.Sprintf("%s, %s - %s, %s, verification code: %s",
			agent.OS(),
			browser,
			version,
			contextutil.RemoteIP(ctx),
			payload.VerificationCode,
		)

		return &pb.AuthorizationResponse{
			Error:     false,
			Message:   meta,
			ExpiredAt: timestamppb.New(expire),
		}, nil
	}

	return &pb.AuthorizationResponse{
		Error:     false,
		Message:   "user created successfully",
		ExpiredAt: timestamppb.New(expire),
	}, nil
}
