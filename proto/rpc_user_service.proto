syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

option go_package = "github.com/liveutil/user_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "User Service API";
    version: "1.0.0";
    description: "User Service API for authentication and user management";
    contact: {
      name: "User Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// UserService service provides authentication and user management endpoints
service UserService {
  // Signing In user with provided mobile.
  rpc SignIn(SignInRequest) returns (AuthorizationResponse) {
    option (google.api.http) = {
      post: "/v1/auth/signin"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Sign In User";
      description: "Authenticate user with mobile number, email, or role";
      security: {};
    };
  }

  // Signing Up user with provided mobile and national identifier.
  rpc SignUp(SignUpUserWithDefaultsRequest) returns (AuthorizationResponse) {
    option (google.api.http) = {
      post: "/v1/auth/signup"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Sign Up User";
      description: "Register a new user with provided information";
      security: {};
    };
  }

  // Otp verification and generating access and refresh tokens.
  rpc OtpVerify(OtpVerifyRequest) returns (AuthenticationResponse) {
    option (google.api.http) = {
      post: "/v1/auth/verify-otp"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Verify OTP";
      description: "Verify OTP code and generate access and refresh tokens";
      security: {};
    };
  }

  // Refreshing existing authentication tokens.
  rpc RefreshToken(RefreshTokenRequest) returns (AuthenticationResponse) {
    option (google.api.http) = {
      post: "/v1/auth/refresh-token"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Refresh Token";
      description: "Refresh existing authentication tokens";
      security: {};
    };
  }

  // Get user context data.
  rpc ContextUser(Empty) returns (ContextUserResponse) {
    option (google.api.http) = {
      get: "/v1/user/context"
    };
  }
}