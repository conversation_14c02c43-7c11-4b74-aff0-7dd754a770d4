syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";
import "rpc_profile.proto";
import "rpc_contact.proto";

option go_package = "github.com/liveutil/user_service/pb";

// User message containing all user data and nested entities
message User {
	// user public identifier to hide real database id of user
	string identifier = 1;
	// user approval status
	bool approved = 2;
	// user ban status
	bool banned = 3;
	// user roles
	repeated string roles = 4;
	// user creation time
	google.protobuf.Timestamp created_at = 5;
	// user profile
	Profile profile = 6;
	// user contact
	Contact contact = 7;
}