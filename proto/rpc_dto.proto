syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";
import "rpc_user.proto";

option go_package = "github.com/liveutil/user_service/pb";

// Define an empty message for methods that require no input.
message Empty {}

// model that contains SignIn request properties.
message SignInRequest {
	// mobile number of user
	string mobile = 1;
	// email address of user
	string email = 2;
	// role of user
	string role = 3;
}

// SignUpUserWithDefaultsRequest contains all parameters needed to create a user with defaults
message SignUpUserWithDefaultsRequest {
	// mobile number of user
	string mobile = 1;
	// email address of user
	string email = 2;
	// profile type of user
	string profile_type = 3;
	// first name of user
	string first_name = 4;
	// last name of user
	string last_name = 5;
	// national id of user
	string national_id = 6;
  }
  
// SignUpUserWithDefaultsResponse contains all created entities include profile and contact
message ContextUserResponse {
	// user data
	User user = 1;
}
  
// model that contains SignIn & SignUp response properties.
message AuthorizationResponse {
	// error sets true if processing failed otherwise sets false
	bool error = 1;
	// message contains error message if error is true or helper message if error is false
	string message = 2;
	// the expiration time of OTP that sent to user's mobile
	google.protobuf.Timestamp expired_at = 3;
}

// model that contains OtpVerify request properties.
message OtpVerifyRequest {
	// mobile number of user
	string mobile = 1;
	// email number of user
	string email = 2;
	// otp code
	string otp_code = 3;
}

// AuthenticationResponse contains all authentication data
message AuthenticationResponse {
	// error sets true if processing failed otherwise sets false
	bool error = 1;
	// message contains error message if error is true or helper message if error is false
	string message = 2;
	// generated access token
	string access_token = 3;
	// the expiration time of generated access token
	google.protobuf.Timestamp access_token_expired_at = 4;
	// generated refresh token
	string refresh_token = 5;
	// the expiration time of generated refresh token
	google.protobuf.Timestamp refresh_token_expired_at = 6;
}

// model that contains RefreshToken request properties.
message RefreshTokenRequest {
	// refresh token
	string refresh_token = 1;
}