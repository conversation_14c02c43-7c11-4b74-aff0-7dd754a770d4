DB_URL=postgresql://close_loop:close_loop_postgres_pass@localhost:5432/close_loop_db?sslmode=disable
PG_CONTAINER=close-loop-postgres
PG_USER=close_loop

.PHONY: help
help: ## Show this help message
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: proto
proto: ## Generate protobuf files, gRPC gateway, and OpenAPI docs (JSON + YAML)
	rm -f pb/*.go
	rm -f docs/*.swagger.json
	rm -f docs/*.yaml
	mkdir -p docs
	protoc --proto_path=proto --go_out=pb --go_opt=paths=source_relative \
	--go-grpc_out=pb --go-grpc_opt=paths=source_relative \
	--grpc-gateway_out=pb --grpc-gateway_opt=paths=source_relative \
	--openapiv2_out=docs --openapiv2_opt=logtostderr=true,json_names_for_fields=false \
	proto/*.proto
	@echo "Converting JSON to YAML..."
	@for file in docs/*.swagger.json; do \
		if [ -f "$$file" ]; then \
			base=$$(basename "$$file" .swagger.json); \
			yq eval -P "$$file" > "docs/$$base.yaml" 2>/dev/null || \
			python3 -c "import json, yaml, sys; yaml.dump(json.load(open('$$file')), sys.stdout, default_flow_style=False)" > "docs/$$base.yaml" 2>/dev/null || \
			echo "Warning: Could not convert $$file to YAML. Install yq or python3 with yaml module."; \
		fi \
	done

.PHONY: proto-yaml
proto-yaml: ## Convert existing OpenAPI JSON files to YAML format
	@echo "Converting existing JSON files to YAML..."
	@for file in docs/*.swagger.json; do \
		if [ -f "$$file" ]; then \
			base=$$(basename "$$file" .swagger.json); \
			yq eval -P "$$file" > "docs/$$base.yaml" 2>/dev/null || \
			python3 -c "import json, yaml, sys; yaml.dump(json.load(open('$$file')), sys.stdout, default_flow_style=False)" > "docs/$$base.yaml" 2>/dev/null || \
			echo "Warning: Could not convert $$file to YAML. Install yq or python3 with yaml module."; \
		fi \
	done

.PHONY: install-proto-deps
install-proto-deps: ## Install protobuf and gRPC-Gateway dependencies
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway@latest
	go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@latest

.PHONY: install-yaml-tools
install-yaml-tools: ## Install tools for YAML conversion (yq and/or PyYAML)
	@echo "Installing YAML conversion tools..."
	@which yq > /dev/null 2>&1 || (echo "Installing yq..." && \
		(brew install yq 2>/dev/null || \
		 go install github.com/mikefarah/yq/v4@latest 2>/dev/null || \
		 echo "Please install yq manually: https://github.com/mikefarah/yq"))
	@python3 -c "import yaml" 2>/dev/null || \
		(echo "Installing Python YAML module..." && pip3 install PyYAML 2>/dev/null || \
		 echo "Please install PyYAML: pip3 install PyYAML")

.PHONY: createdb
createdb:
	docker exec -it "$(PG_CONTAINER)" createdb --username="$(PG_USER)" --owner="$(PG_USER)" "$(PG_USER)"

.PHONY: dropdb
dropdb:
	docker exec -it "$(PG_CONTAINER)" dropdb --username="$(PG_USER)" "$(PG_USER)"

.PHONY: migrateup
migrateup:
	migrate -path internal/infrastructure/db/migrations -database "$(DB_URL)" -verbose up

.PHONY: migratedown
migratedown:
	migrate -path internal/infrastructure/db/migrations -database "$(DB_URL)" -verbose down

.PHONY: dbdump
dbdump:
	docker exec -i "$(PG_CONTAINER)" psql -U "$(PG_USER)" -d "$(PG_USER)" < internal/infrastructure/db/dump.sql

.PHONY: dbrestore
dbbackup:
	docker exec -t "$(PG_CONTAINER)" pg_dumpall -c -U "$(PG_USER)" > dbbackup/backup.sql

.PHONY: dbbackup
dbrestore:
	docker exec -i "$(PG_CONTAINER)" psql -U "$(PG_USER)" -d "$(PG_USER)" < dbbackup/backup.sql

.PHONY: sqlc
sqlc:
	@sqlc generate -f ./internal/infrastructure/db/sqlc.yaml

.PHONY: schema
schema:
	dbml2sql internal/infrastructure/db/diagram.dbml --postgres --out-file internal/infrastructure/db/schema.sql

.PHONY: compose
compose:
	docker compose up -d

.PHONY: dev-up
dev-up:
	docker compose -f docker-compose.dev.yaml up -d

.PHONY: dev-down
dev-down:
	docker compose -f docker-compose.dev.yaml down -v 

.PHONY: audit
audit:
	gosec ./...

.PHONY: heap-prof
heap-prof:
	go tool pprof -http localhost:6161 http://localhost:6060/debug/pprof/heap

.PHONY: docker-build
docker-build:
	docker build --tag user-service:latest .

.PHONY: run
run:
	go run ./cmd/user-service/main.go

.PHONY: tidy
tidy:
	export GOPRIVATE=github.com/liveutil/*
	go mod tidy

.PHONY: golib
golib:
	GOPROXY=direct GOPRIVATE=github.com/liveutil/* go get -u github.com/liveutil/go-lib

.PHONY: test
test:
	go test -v ./test/...

.PHONY: test-api
test-api:
	@echo "Starting servers for API testing..."
	@echo "Make sure to run 'make run' in another terminal first"
	@echo "Then run: make test"

.PHONY: openapi-yaml
openapi-yaml:
	@echo "OpenAPI 3.0 spec is available at: docs/user_service_openapi.yaml"
	@echo "Swagger 2.0 spec is available at: docs/user_service.swagger.json"

.PHONY: validate-openapi
validate-openapi:
	./scripts/validate-openapi.sh