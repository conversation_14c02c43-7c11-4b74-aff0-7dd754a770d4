package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	pb "github.com/liveutil/user_service/pb"
)

const (
	grpcServerAddr = "localhost:8080"
	httpServerAddr = "http://localhost:8081"
)

// TestGRPCSignIn tests the gRPC SignIn endpoint
func TestGRPCSignIn(t *testing.T) {
	// Connect to gRPC server
	conn, err := grpc.Dial(grpcServerAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		t.Fatalf("Failed to connect to gRPC server: %v", err)
	}
	defer conn.Close()

	client := pb.NewUserServiceClient(conn)

	// Test SignIn request
	req := &pb.SignInRequest{
		Mobile: "+1234567890",
		Email:  "<EMAIL>",
		Role:   "user",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := client.SignIn(ctx, req)
	if err != nil {
		t.Fatalf("SignIn failed: %v", err)
	}

	if resp == nil {
		t.Fatal("Response is nil")
	}

	t.Logf("gRPC SignIn response: %+v", resp)
}

// TestHTTPSignIn tests the HTTP SignIn endpoint
func TestHTTPSignIn(t *testing.T) {
	// Prepare request body
	reqBody := map[string]string{
		"mobile": "+1234567890",
		"email":  "<EMAIL>",
		"role":   "user",
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		t.Fatalf("Failed to marshal request body: %v", err)
	}

	// Make HTTP request
	resp, err := http.Post(
		fmt.Sprintf("%s/v1/auth/signin", httpServerAddr),
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	if err != nil {
		t.Fatalf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status 200, got %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	t.Logf("HTTP SignIn response: %+v", response)
}

// TestGRPCSignUp tests the gRPC SignUp endpoint
func TestGRPCSignUp(t *testing.T) {
	// Connect to gRPC server
	conn, err := grpc.Dial(grpcServerAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		t.Fatalf("Failed to connect to gRPC server: %v", err)
	}
	defer conn.Close()

	client := pb.NewUserServiceClient(conn)

	// Test SignUp request
	req := &pb.SignUpUserWithDefaultsRequest{
		Mobile:      "+1234567890",
		Email:       "<EMAIL>",
		ProfileType: "individual",
		FirstName:   "John",
		LastName:    "Doe",
		NationalId:  "1234567890",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, err := client.SignUp(ctx, req)
	if err != nil {
		t.Fatalf("SignUp failed: %v", err)
	}

	if resp == nil {
		t.Fatal("Response is nil")
	}

	t.Logf("gRPC SignUp response: %+v", resp)
}

// TestHTTPSignUp tests the HTTP SignUp endpoint
func TestHTTPSignUp(t *testing.T) {
	// Prepare request body
	reqBody := map[string]string{
		"mobile":      "+1234567890",
		"email":       "<EMAIL>",
		"profileType": "individual",
		"firstName":   "John",
		"lastName":    "Doe",
		"nationalId":  "1234567890",
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		t.Fatalf("Failed to marshal request body: %v", err)
	}

	// Make HTTP request
	resp, err := http.Post(
		fmt.Sprintf("%s/v1/auth/signup", httpServerAddr),
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	if err != nil {
		t.Fatalf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status 200, got %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	t.Logf("HTTP SignUp response: %+v", response)
}

// TestHTTPHealthCheck tests if the HTTP server is running
func TestHTTPHealthCheck(t *testing.T) {
	resp, err := http.Get(fmt.Sprintf("%s/v1/user/context", httpServerAddr))
	if err != nil {
		t.Fatalf("HTTP health check failed: %v", err)
	}
	defer resp.Body.Close()

	// We expect this to fail with 401 or similar since we're not authenticated
	// but it should not fail with connection errors
	if resp.StatusCode == 0 {
		t.Fatal("No response from HTTP server")
	}

	t.Logf("HTTP health check status: %d", resp.StatusCode)
}

// TestOpenAPISpec tests if the OpenAPI spec is accessible
func TestOpenAPISpec(t *testing.T) {
	// This is a basic test to ensure our OpenAPI spec file exists
	// In a real scenario, you might serve this via HTTP
	t.Log("OpenAPI spec file should be available at docs/user_service_openapi.yaml")
	t.Log("Swagger 2.0 spec is available at docs/user_service.swagger.json")
}
