// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: rpc_user_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_user_service_proto protoreflect.FileDescriptor

const file_rpc_user_service_proto_rawDesc = "" +
	"\n" +
	"\x16rpc_user_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto2\xed\x05\n" +
	"\vUserService\x12\x9b\x01\n" +
	"\x06SignIn\x12\x11.pb.SignInRequest\x1a\x19.pb.AuthorizationResponse\"c\x92AF\x12\fSign In User\x1a4Authenticate user with mobile number, email, or roleb\x00\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/auth/signin\x12\xa4\x01\n" +
	"\x06SignUp\x12!.pb.SignUpUserWithDefaultsRequest\x1a\x19.pb.AuthorizationResponse\"\\\x92A?\x12\fSign Up User\x1a-Register a new user with provided informationb\x00\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/auth/signup\x12\xa6\x01\n" +
	"\tOtpVerify\x12\x14.pb.OtpVerifyRequest\x1a\x1a.pb.AuthenticationResponse\"g\x92AF\x12\n" +
	"Verify OTP\x1a6Verify OTP code and generate access and refresh tokensb\x00\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/auth/verify-otp\x12\xa2\x01\n" +
	"\fRefreshToken\x12\x17.pb.RefreshTokenRequest\x1a\x1a.pb.AuthenticationResponse\"]\x92A9\x12\rRefresh Token\x1a&Refresh existing authentication tokensb\x00\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/auth/refresh-token\x12K\n" +
	"\vContextUser\x12\t.pb.Empty\x1a\x17.pb.ContextUserResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/v1/user/contextB\xc0\x02\x92A\x97\x02\x12\x83\x01\n" +
	"\x10User Service API\x127User Service API for authentication and user management\"/\n" +
	"\x18User Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z#github.com/liveutil/user_service/pbb\x06proto3"

var file_rpc_user_service_proto_goTypes = []any{
	(*SignInRequest)(nil),                 // 0: pb.SignInRequest
	(*SignUpUserWithDefaultsRequest)(nil), // 1: pb.SignUpUserWithDefaultsRequest
	(*OtpVerifyRequest)(nil),              // 2: pb.OtpVerifyRequest
	(*RefreshTokenRequest)(nil),           // 3: pb.RefreshTokenRequest
	(*Empty)(nil),                         // 4: pb.Empty
	(*AuthorizationResponse)(nil),         // 5: pb.AuthorizationResponse
	(*AuthenticationResponse)(nil),        // 6: pb.AuthenticationResponse
	(*ContextUserResponse)(nil),           // 7: pb.ContextUserResponse
}
var file_rpc_user_service_proto_depIdxs = []int32{
	0, // 0: pb.UserService.SignIn:input_type -> pb.SignInRequest
	1, // 1: pb.UserService.SignUp:input_type -> pb.SignUpUserWithDefaultsRequest
	2, // 2: pb.UserService.OtpVerify:input_type -> pb.OtpVerifyRequest
	3, // 3: pb.UserService.RefreshToken:input_type -> pb.RefreshTokenRequest
	4, // 4: pb.UserService.ContextUser:input_type -> pb.Empty
	5, // 5: pb.UserService.SignIn:output_type -> pb.AuthorizationResponse
	5, // 6: pb.UserService.SignUp:output_type -> pb.AuthorizationResponse
	6, // 7: pb.UserService.OtpVerify:output_type -> pb.AuthenticationResponse
	6, // 8: pb.UserService.RefreshToken:output_type -> pb.AuthenticationResponse
	7, // 9: pb.UserService.ContextUser:output_type -> pb.ContextUserResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_user_service_proto_init() }
func file_rpc_user_service_proto_init() {
	if File_rpc_user_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_user_service_proto_rawDesc), len(file_rpc_user_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_user_service_proto_goTypes,
		DependencyIndexes: file_rpc_user_service_proto_depIdxs,
	}.Build()
	File_rpc_user_service_proto = out.File
	file_rpc_user_service_proto_goTypes = nil
	file_rpc_user_service_proto_depIdxs = nil
}
