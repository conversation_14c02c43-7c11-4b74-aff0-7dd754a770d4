// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Define an empty message for methods that require no input.
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

// model that contains SignIn request properties.
type SignInRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mobile number of user
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// email address of user
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// role of user
	Role          string `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInRequest) Reset() {
	*x = SignInRequest{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInRequest) ProtoMessage() {}

func (x *SignInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInRequest.ProtoReflect.Descriptor instead.
func (*SignInRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *SignInRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SignInRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SignInRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

// SignUpUserWithDefaultsRequest contains all parameters needed to create a user with defaults
type SignUpUserWithDefaultsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mobile number of user
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// email address of user
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// profile type of user
	ProfileType string `protobuf:"bytes,3,opt,name=profile_type,json=profileType,proto3" json:"profile_type,omitempty"`
	// first name of user
	FirstName string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name of user
	LastName string `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// national id of user
	NationalId    string `protobuf:"bytes,6,opt,name=national_id,json=nationalId,proto3" json:"national_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignUpUserWithDefaultsRequest) Reset() {
	*x = SignUpUserWithDefaultsRequest{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUpUserWithDefaultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpUserWithDefaultsRequest) ProtoMessage() {}

func (x *SignUpUserWithDefaultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpUserWithDefaultsRequest.ProtoReflect.Descriptor instead.
func (*SignUpUserWithDefaultsRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *SignUpUserWithDefaultsRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SignUpUserWithDefaultsRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SignUpUserWithDefaultsRequest) GetProfileType() string {
	if x != nil {
		return x.ProfileType
	}
	return ""
}

func (x *SignUpUserWithDefaultsRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SignUpUserWithDefaultsRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *SignUpUserWithDefaultsRequest) GetNationalId() string {
	if x != nil {
		return x.NationalId
	}
	return ""
}

// SignUpUserWithDefaultsResponse contains all created entities include profile and contact
type ContextUserResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user data
	User          *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContextUserResponse) Reset() {
	*x = ContextUserResponse{}
	mi := &file_rpc_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContextUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextUserResponse) ProtoMessage() {}

func (x *ContextUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextUserResponse.ProtoReflect.Descriptor instead.
func (*ContextUserResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{3}
}

func (x *ContextUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// model that contains SignIn & SignUp response properties.
type AuthorizationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// error sets true if processing failed otherwise sets false
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// message contains error message if error is true or helper message if error is false
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// the expiration time of OTP that sent to user's mobile
	ExpiredAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthorizationResponse) Reset() {
	*x = AuthorizationResponse{}
	mi := &file_rpc_dto_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthorizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizationResponse) ProtoMessage() {}

func (x *AuthorizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizationResponse.ProtoReflect.Descriptor instead.
func (*AuthorizationResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{4}
}

func (x *AuthorizationResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *AuthorizationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AuthorizationResponse) GetExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiredAt
	}
	return nil
}

// model that contains OtpVerify request properties.
type OtpVerifyRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mobile number of user
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// email number of user
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// otp code
	OtpCode       string `protobuf:"bytes,3,opt,name=otp_code,json=otpCode,proto3" json:"otp_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OtpVerifyRequest) Reset() {
	*x = OtpVerifyRequest{}
	mi := &file_rpc_dto_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OtpVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtpVerifyRequest) ProtoMessage() {}

func (x *OtpVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtpVerifyRequest.ProtoReflect.Descriptor instead.
func (*OtpVerifyRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{5}
}

func (x *OtpVerifyRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *OtpVerifyRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OtpVerifyRequest) GetOtpCode() string {
	if x != nil {
		return x.OtpCode
	}
	return ""
}

// AuthenticationResponse contains all authentication data
type AuthenticationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// error sets true if processing failed otherwise sets false
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// message contains error message if error is true or helper message if error is false
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// generated access token
	AccessToken string `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// the expiration time of generated access token
	AccessTokenExpiredAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=access_token_expired_at,json=accessTokenExpiredAt,proto3" json:"access_token_expired_at,omitempty"`
	// generated refresh token
	RefreshToken string `protobuf:"bytes,5,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	// the expiration time of generated refresh token
	RefreshTokenExpiredAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=refresh_token_expired_at,json=refreshTokenExpiredAt,proto3" json:"refresh_token_expired_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AuthenticationResponse) Reset() {
	*x = AuthenticationResponse{}
	mi := &file_rpc_dto_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthenticationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticationResponse) ProtoMessage() {}

func (x *AuthenticationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticationResponse.ProtoReflect.Descriptor instead.
func (*AuthenticationResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{6}
}

func (x *AuthenticationResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *AuthenticationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *AuthenticationResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *AuthenticationResponse) GetAccessTokenExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AccessTokenExpiredAt
	}
	return nil
}

func (x *AuthenticationResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *AuthenticationResponse) GetRefreshTokenExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RefreshTokenExpiredAt
	}
	return nil
}

// model that contains RefreshToken request properties.
type RefreshTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// refresh token
	RefreshToken  string `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	mi := &file_rpc_dto_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{7}
}

func (x *RefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x0erpc_user.proto\"\a\n" +
	"\x05Empty\"Q\n" +
	"\rSignInRequest\x12\x16\n" +
	"\x06mobile\x18\x01 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x12\n" +
	"\x04role\x18\x03 \x01(\tR\x04role\"\xcd\x01\n" +
	"\x1dSignUpUserWithDefaultsRequest\x12\x16\n" +
	"\x06mobile\x18\x01 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12!\n" +
	"\fprofile_type\x18\x03 \x01(\tR\vprofileType\x12\x1d\n" +
	"\n" +
	"first_name\x18\x04 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x05 \x01(\tR\blastName\x12\x1f\n" +
	"\vnational_id\x18\x06 \x01(\tR\n" +
	"nationalId\"3\n" +
	"\x13ContextUserResponse\x12\x1c\n" +
	"\x04user\x18\x01 \x01(\v2\b.pb.UserR\x04user\"\x82\x01\n" +
	"\x15AuthorizationResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x129\n" +
	"\n" +
	"expired_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\texpiredAt\"[\n" +
	"\x10OtpVerifyRequest\x12\x16\n" +
	"\x06mobile\x18\x01 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x19\n" +
	"\botp_code\x18\x03 \x01(\tR\aotpCode\"\xb8\x02\n" +
	"\x16AuthenticationResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\faccess_token\x18\x03 \x01(\tR\vaccessToken\x12Q\n" +
	"\x17access_token_expired_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x14accessTokenExpiredAt\x12#\n" +
	"\rrefresh_token\x18\x05 \x01(\tR\frefreshToken\x12S\n" +
	"\x18refresh_token_expired_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x15refreshTokenExpiredAt\":\n" +
	"\x13RefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshTokenB%Z#github.com/liveutil/user_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_rpc_dto_proto_goTypes = []any{
	(*Empty)(nil),                         // 0: pb.Empty
	(*SignInRequest)(nil),                 // 1: pb.SignInRequest
	(*SignUpUserWithDefaultsRequest)(nil), // 2: pb.SignUpUserWithDefaultsRequest
	(*ContextUserResponse)(nil),           // 3: pb.ContextUserResponse
	(*AuthorizationResponse)(nil),         // 4: pb.AuthorizationResponse
	(*OtpVerifyRequest)(nil),              // 5: pb.OtpVerifyRequest
	(*AuthenticationResponse)(nil),        // 6: pb.AuthenticationResponse
	(*RefreshTokenRequest)(nil),           // 7: pb.RefreshTokenRequest
	(*User)(nil),                          // 8: pb.User
	(*timestamppb.Timestamp)(nil),         // 9: google.protobuf.Timestamp
}
var file_rpc_dto_proto_depIdxs = []int32{
	8, // 0: pb.ContextUserResponse.user:type_name -> pb.User
	9, // 1: pb.AuthorizationResponse.expired_at:type_name -> google.protobuf.Timestamp
	9, // 2: pb.AuthenticationResponse.access_token_expired_at:type_name -> google.protobuf.Timestamp
	9, // 3: pb.AuthenticationResponse.refresh_token_expired_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
