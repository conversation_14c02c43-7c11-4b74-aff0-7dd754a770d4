{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "OtpVerifyRequest", "$protected": true, "title": "OtpVerifyRequest", "description": "Request to verify TOTP code for mobile or email", "type": "object", "required": ["otp_code"], "oneOf": [{"required": ["mobile"]}, {"required": ["email"]}], "properties": {"mobile": {"type": "string", "description": "Mobile phone number", "pattern": "^\\+98[0-9]{10}$", "minLength": 13, "maxLength": 16}, "email": {"type": "string", "description": "Email address", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "maxLength": 256}, "otp_code": {"type": "string", "description": "TOTP code", "minLength": 4, "maxLength": 8, "pattern": "^[0-9]{4,8}$"}}, "additionalProperties": false}