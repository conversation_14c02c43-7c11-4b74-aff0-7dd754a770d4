consumes:
- application/json
definitions:
  pbDetails:
    properties:
      database:
        title: health status of the database
        type: string
      message_bus:
        title: health status of the message bus (NATS)
        type: string
      mongodb:
        title: health status of the mongodb
        type: string
      redis:
        title: health status of the redis
        type: string
      service_mesh:
        title: health status of the service mesh (Dapr) or (Builtin NATS based service
          mesh client)
        type: string
    title: Details of the health status of the service and required services
    type: object
  pbHealthCheckResponse:
    properties:
      details:
        $ref: '#/definitions/pbDetails'
        title: details of the health status of the service and required services
      status:
        title: health status of the service and required services
        type: string
      timestamp:
        format: int64
        title: timestamp of the health check
        type: string
    title: Response for checking health status of the service and required services
    type: object
  protobufAny:
    additionalProperties: {}
    properties:
      '@type':
        type: string
    type: object
  rpcStatus:
    properties:
      code:
        format: int32
        type: integer
      details:
        items:
          $ref: '#/definitions/protobufAny'
          type: object
        type: array
      message:
        type: string
    type: object
info:
  title: rpc_health.proto
  version: version not set
paths: {}
produces:
- application/json
swagger: '2.0'
tags:
- name: Health
