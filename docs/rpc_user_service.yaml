consumes:
- application/json
definitions:
  pbAuthenticationResponse:
    properties:
      access_token:
        title: generated access token
        type: string
      access_token_expired_at:
        format: date-time
        title: the expiration time of generated access token
        type: string
      error:
        title: error sets true if processing failed otherwise sets false
        type: boolean
      message:
        title: message contains error message if error is true or helper message if
          error is false
        type: string
      refresh_token:
        title: generated refresh token
        type: string
      refresh_token_expired_at:
        format: date-time
        title: the expiration time of generated refresh token
        type: string
    title: AuthenticationResponse contains all authentication data
    type: object
  pbAuthorizationResponse:
    description: model that contains SignIn & SignUp response properties.
    properties:
      error:
        title: error sets true if processing failed otherwise sets false
        type: boolean
      expired_at:
        format: date-time
        title: the expiration time of OTP that sent to user's mobile
        type: string
      message:
        title: message contains error message if error is true or helper message if
          error is false
        type: string
    type: object
  pbContact:
    properties:
      created_at:
        format: date-time
        type: string
      email:
        title: contact email address
        type: string
      identifier:
        title: contact public identifier to hide real database id of contact
        type: string
      meta_data:
        title: contact meta data
        type: string
      mobile:
        title: contact mobile number
        type: string
    title: Contact message containing all contact data
    type: object
  pbContextUserResponse:
    properties:
      user:
        $ref: '#/definitions/pbUser'
        title: user data
    title: SignUpUserWithDefaultsResponse contains all created entities include profile
      and contact
    type: object
  pbOtpVerifyRequest:
    description: model that contains OtpVerify request properties.
    properties:
      email:
        title: email number of user
        type: string
      mobile:
        title: mobile number of user
        type: string
      otp_code:
        title: otp code
        type: string
    type: object
  pbProfile:
    properties:
      created_at:
        format: date-time
        title: profile creation time
        type: string
      first_name:
        title: profile first name
        type: string
      identifier:
        title: profile public identifier to hide real database id of profile
        type: string
      last_name:
        type: string
      meta_data:
        title: profile meta data
        type: string
      national_id:
        title: profile national id
        type: string
      profile_type:
        title: profile type
        type: string
      status:
        title: profile status
        type: string
    title: Profile message containing all profile data
    type: object
  pbRefreshTokenRequest:
    description: model that contains RefreshToken request properties.
    properties:
      refresh_token:
        title: refresh token
        type: string
    type: object
  pbSignInRequest:
    description: model that contains SignIn request properties.
    properties:
      email:
        title: email address of user
        type: string
      mobile:
        title: mobile number of user
        type: string
      role:
        title: role of user
        type: string
    type: object
  pbSignUpUserWithDefaultsRequest:
    properties:
      email:
        title: email address of user
        type: string
      first_name:
        title: first name of user
        type: string
      last_name:
        title: last name of user
        type: string
      mobile:
        title: mobile number of user
        type: string
      national_id:
        title: national id of user
        type: string
      profile_type:
        title: profile type of user
        type: string
    title: SignUpUserWithDefaultsRequest contains all parameters needed to create
      a user with defaults
    type: object
  pbUser:
    properties:
      approved:
        title: user approval status
        type: boolean
      banned:
        title: user ban status
        type: boolean
      contact:
        $ref: '#/definitions/pbContact'
        title: user contact
      created_at:
        format: date-time
        title: user creation time
        type: string
      identifier:
        title: user public identifier to hide real database id of user
        type: string
      profile:
        $ref: '#/definitions/pbProfile'
        title: user profile
      roles:
        items:
          type: string
        title: user roles
        type: array
    title: User message containing all user data and nested entities
    type: object
  protobufAny:
    additionalProperties: {}
    properties:
      '@type':
        type: string
    type: object
  rpcStatus:
    properties:
      code:
        format: int32
        type: integer
      details:
        items:
          $ref: '#/definitions/protobufAny'
          type: object
        type: array
      message:
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
    name: User Service API Support
  description: User Service API for authentication and user management
  title: User Service API
  version: 1.0.0
paths:
  /v1/auth/refresh-token:
    post:
      description: Refresh existing authentication tokens
      operationId: UserService_RefreshToken
      parameters:
      - description: model that contains RefreshToken request properties.
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/pbRefreshTokenRequest'
      responses:
        '200':
          description: A successful response.
          schema:
            $ref: '#/definitions/pbAuthenticationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpcStatus'
      security: []
      summary: Refresh Token
      tags:
      - UserService
  /v1/auth/signin:
    post:
      description: Authenticate user with mobile number, email, or role
      operationId: UserService_SignIn
      parameters:
      - description: model that contains SignIn request properties.
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/pbSignInRequest'
      responses:
        '200':
          description: A successful response.
          schema:
            $ref: '#/definitions/pbAuthorizationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpcStatus'
      security: []
      summary: Sign In User
      tags:
      - UserService
  /v1/auth/signup:
    post:
      description: Register a new user with provided information
      operationId: UserService_SignUp
      parameters:
      - in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/pbSignUpUserWithDefaultsRequest'
      responses:
        '200':
          description: A successful response.
          schema:
            $ref: '#/definitions/pbAuthorizationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpcStatus'
      security: []
      summary: Sign Up User
      tags:
      - UserService
  /v1/auth/verify-otp:
    post:
      description: Verify OTP code and generate access and refresh tokens
      operationId: UserService_OtpVerify
      parameters:
      - description: model that contains OtpVerify request properties.
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/pbOtpVerifyRequest'
      responses:
        '200':
          description: A successful response.
          schema:
            $ref: '#/definitions/pbAuthenticationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpcStatus'
      security: []
      summary: Verify OTP
      tags:
      - UserService
  /v1/user/context:
    get:
      operationId: UserService_ContextUser
      responses:
        '200':
          description: A successful response.
          schema:
            $ref: '#/definitions/pbContextUserResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpcStatus'
      summary: Get user context data.
      tags:
      - UserService
produces:
- application/json
schemes:
- http
- https
security:
- Bearer: []
securityDefinitions:
  Bearer:
    description: 'Authentication token, prefixed by Bearer: Bearer <token>'
    in: header
    name: Authorization
    type: apiKey
swagger: '2.0'
tags:
- name: UserService
