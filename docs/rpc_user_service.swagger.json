{"swagger": "2.0", "info": {"title": "User Service API", "description": "User Service API for authentication and user management", "version": "1.0.0", "contact": {"name": "User Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "UserService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/auth/refresh-token": {"post": {"summary": "Refresh <PERSON>", "description": "Refresh existing authentication tokens", "operationId": "UserService_RefreshToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbAuthenticationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains RefreshToken request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbRefreshTokenRequest"}}], "tags": ["UserService"], "security": []}}, "/v1/auth/signin": {"post": {"summary": "Sign In User", "description": "Authenticate user with mobile number, email, or role", "operationId": "UserService_SignIn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbAuthorizationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains SignIn request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSignInRequest"}}], "tags": ["UserService"], "security": []}}, "/v1/auth/signup": {"post": {"summary": "Sign Up User", "description": "Register a new user with provided information", "operationId": "UserService_SignUp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbAuthorizationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSignUpUserWithDefaultsRequest"}}], "tags": ["UserService"], "security": []}}, "/v1/auth/verify-otp": {"post": {"summary": "Verify OTP", "description": "Verify OTP code and generate access and refresh tokens", "operationId": "UserService_OtpVerify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbAuthenticationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains OtpVerify request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOtpVerifyRequest"}}], "tags": ["UserService"], "security": []}}, "/v1/user/context": {"get": {"summary": "Get user context data.", "operationId": "UserService_ContextUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbContextUserResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}}, "definitions": {"pbAuthenticationResponse": {"type": "object", "properties": {"error": {"type": "boolean", "title": "error sets true if processing failed otherwise sets false"}, "message": {"type": "string", "title": "message contains error message if error is true or helper message if error is false"}, "access_token": {"type": "string", "title": "generated access token"}, "access_token_expired_at": {"type": "string", "format": "date-time", "title": "the expiration time of generated access token"}, "refresh_token": {"type": "string", "title": "generated refresh token"}, "refresh_token_expired_at": {"type": "string", "format": "date-time", "title": "the expiration time of generated refresh token"}}, "title": "AuthenticationResponse contains all authentication data"}, "pbAuthorizationResponse": {"type": "object", "properties": {"error": {"type": "boolean", "title": "error sets true if processing failed otherwise sets false"}, "message": {"type": "string", "title": "message contains error message if error is true or helper message if error is false"}, "expired_at": {"type": "string", "format": "date-time", "title": "the expiration time of OTP that sent to user's mobile"}}, "description": "model that contains SignIn & SignUp response properties."}, "pbContact": {"type": "object", "properties": {"identifier": {"type": "string", "title": "contact public identifier to hide real database id of contact"}, "mobile": {"type": "string", "title": "contact mobile number"}, "email": {"type": "string", "title": "contact email address"}, "meta_data": {"type": "string", "title": "contact meta data"}, "created_at": {"type": "string", "format": "date-time"}}, "title": "Contact message containing all contact data"}, "pbContextUserResponse": {"type": "object", "properties": {"user": {"$ref": "#/definitions/pbUser", "title": "user data"}}, "title": "SignUpUserWithDefaultsResponse contains all created entities include profile and contact"}, "pbOtpVerifyRequest": {"type": "object", "properties": {"mobile": {"type": "string", "title": "mobile number of user"}, "email": {"type": "string", "title": "email number of user"}, "otp_code": {"type": "string", "title": "otp code"}}, "description": "model that contains OtpVerify request properties."}, "pbProfile": {"type": "object", "properties": {"identifier": {"type": "string", "title": "profile public identifier to hide real database id of profile"}, "profile_type": {"type": "string", "title": "profile type"}, "first_name": {"type": "string", "title": "profile first name"}, "last_name": {"type": "string"}, "national_id": {"type": "string", "title": "profile national id"}, "status": {"type": "string", "title": "profile status"}, "meta_data": {"type": "string", "title": "profile meta data"}, "created_at": {"type": "string", "format": "date-time", "title": "profile creation time"}}, "title": "Profile message containing all profile data"}, "pbRefreshTokenRequest": {"type": "object", "properties": {"refresh_token": {"type": "string", "title": "refresh token"}}, "description": "model that contains RefreshToken request properties."}, "pbSignInRequest": {"type": "object", "properties": {"mobile": {"type": "string", "title": "mobile number of user"}, "email": {"type": "string", "title": "email address of user"}, "role": {"type": "string", "title": "role of user"}}, "description": "model that contains SignIn request properties."}, "pbSignUpUserWithDefaultsRequest": {"type": "object", "properties": {"mobile": {"type": "string", "title": "mobile number of user"}, "email": {"type": "string", "title": "email address of user"}, "profile_type": {"type": "string", "title": "profile type of user"}, "first_name": {"type": "string", "title": "first name of user"}, "last_name": {"type": "string", "title": "last name of user"}, "national_id": {"type": "string", "title": "national id of user"}}, "title": "SignUpUserWithDefaultsRequest contains all parameters needed to create a user with defaults"}, "pbUser": {"type": "object", "properties": {"identifier": {"type": "string", "title": "user public identifier to hide real database id of user"}, "approved": {"type": "boolean", "title": "user approval status"}, "banned": {"type": "boolean", "title": "user ban status"}, "roles": {"type": "array", "items": {"type": "string"}, "title": "user roles"}, "created_at": {"type": "string", "format": "date-time", "title": "user creation time"}, "profile": {"$ref": "#/definitions/pbProfile", "title": "user profile"}, "contact": {"$ref": "#/definitions/pbContact", "title": "user contact"}}, "title": "User message containing all user data and nested entities"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}