consumes:
- application/json
definitions:
  protobufAny:
    additionalProperties: {}
    properties:
      '@type':
        type: string
    type: object
  rpcStatus:
    properties:
      code:
        format: int32
        type: integer
      details:
        items:
          $ref: '#/definitions/protobufAny'
          type: object
        type: array
      message:
        type: string
    type: object
info:
  title: rpc_profile.proto
  version: version not set
paths: {}
produces:
- application/json
swagger: '2.0'
